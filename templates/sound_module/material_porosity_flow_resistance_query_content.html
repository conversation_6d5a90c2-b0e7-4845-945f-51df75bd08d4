<!-- 材料孔隙率流阻查询页面内容 - 用于AJAX加载 -->
<!-- 查询条件卡片 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="query-form">
            <div class="row g-3 align-items-start">
                <!-- 零件选择 -->
                <div class="col-md-4">
                    <select class="form-select" id="part-select">
                        <option value="">加载中...</option>
                    </select>
                </div>

                <!-- 材料选择 -->
                <div class="col-md-4">
                    <div class="material-multiselect" id="material-multiselect">
                        <div class="multiselect-container">
                            <div class="multiselect-input-container">
                                <input type="text" class="form-control multiselect-input" placeholder="请先选择零件..." readonly>
                                <i class="fas fa-chevron-down multiselect-arrow"></i>
                            </div>
                            <div class="multiselect-dropdown">
                                <div class="multiselect-search">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索材料...">
                                </div>
                                <div class="multiselect-options">
                                    <!-- 动态加载选项 -->
                                </div>
                            </div>
                        </div>
                        <div class="selected-items mt-2">
                            <!-- 已选择的材料标签 -->
                        </div>
                    </div>
                </div>

                <!-- 查询按钮 -->
                <div class="col-md-4 d-flex align-items-start">
                    <button type="button" class="btn btn-primary" id="query-btn" disabled>
                        <i class="fas fa-search me-1"></i>查询
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>孔隙率流阻查询结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span id="material-count" class="badge bg-secondary">0 个材料</span>
            <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                <i class="fas fa-download me-1"></i>导出数据
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- 数据表格 -->
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="results-table">
                <thead class="table-dark">
                    <tr>
                        <th>材料名称</th>
                        <th>厚度 (mm)</th>
                        <th>密度 (kg/m³)</th>
                        <th>孔隙率 (%)</th>
                        <th>流阻 (Pa·s/m²)</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-filter fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择查询条件</h5>
        <p class="text-muted">选择零件和材料，点击"查询"按钮查看孔隙率流阻数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在查询数据...</p>
</div>

<!-- 内联脚本确保在标签页中正确初始化 -->
<script>
// 动态加载CSS
if (!document.querySelector('link[href*="material_porosity_flow_resistance_query.css"]')) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/static/css/material_porosity_flow_resistance_query.css';
    document.head.appendChild(link);
}

// 动态加载JS并初始化
if (!window.materialPorosityScriptLoaded) {
    const script = document.createElement('script');
    script.src = '/static/js/material_porosity_flow_resistance.js';
    script.onload = function() {
        window.materialPorosityScriptLoaded = true;
        // 初始化孔隙率流阻查询页面
        if (typeof MaterialPorosityFlowResistanceQuery === 'function') {
            new MaterialPorosityFlowResistanceQuery();
        }
    };
    document.head.appendChild(script);
} else {
    // 如果脚本已加载，直接初始化
    if (typeof MaterialPorosityFlowResistanceQuery === 'function') {
        new MaterialPorosityFlowResistanceQuery();
    }
}
</script>
