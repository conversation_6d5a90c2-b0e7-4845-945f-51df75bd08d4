from flask import Blueprint, render_template, request, send_file
from decorators import login_required
from utils.result import success, error, bad_request
from services.material_porosity_flow_resistance_service import material_porosity_flow_resistance_service
import io


# 创建蓝图
material_porosity_flow_resistance_bp = Blueprint(
    'material_porosity_flow_resistance', 
    __name__, 
    url_prefix='/material_porosity_flow_resistance'
)


class MaterialPorosityFlowResistanceController:
    """材料孔隙率流阻控制器"""
    
    @staticmethod
    @material_porosity_flow_resistance_bp.route('/query')
    @login_required
    def query_page():
        """孔隙率流阻查询页面"""
        # 检查是否是AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.args.get('ajax') == '1':
            # 返回不包含base模板的内容
            return render_template('sound_module/material_porosity_flow_resistance_query_content.html')
        else:
            # 返回完整页面
            return render_template('sound_module/material_porosity_flow_resistance_query.html')
    
    @staticmethod
    @material_porosity_flow_resistance_bp.route('/api/parts')
    @login_required
    def get_parts():
        """获取零件列表"""
        try:
            parts = material_porosity_flow_resistance_service.get_part_list()
            return success(parts)
        except Exception as e:
            return error(f"获取零件列表失败: {str(e)}")
    
    @staticmethod
    @material_porosity_flow_resistance_bp.route('/api/materials')
    @login_required
    def get_materials():
        """获取材料列表"""
        try:
            part_names = request.args.getlist('part_names')
            materials = material_porosity_flow_resistance_service.get_material_list(
                part_names=part_names if part_names else None
            )
            return success(materials)
        except Exception as e:
            return error(f"获取材料列表失败: {str(e)}")
    
    @staticmethod
    @material_porosity_flow_resistance_bp.route('/api/query_data')
    @login_required
    def query_data():
        """查询孔隙率流阻数据"""
        try:
            part_names = request.args.getlist('part_names')
            material_names = request.args.getlist('material_names')
            
            if not part_names and not material_names:
                return bad_request("请至少选择一个零件或材料")
            
            data = material_porosity_flow_resistance_service.query_data(
                part_names=part_names if part_names else None,
                material_names=material_names if material_names else None
            )
            
            if not data:
                return error("未找到匹配的数据")
            
            return success(data, "数据查询成功")
            
        except Exception as e:
            return error(f"查询数据失败: {str(e)}")
    
    @staticmethod
    @material_porosity_flow_resistance_bp.route('/api/export_csv')
    @login_required
    def export_csv():
        """导出CSV数据"""
        try:
            part_names = request.args.getlist('part_names')
            material_names = request.args.getlist('material_names')
            
            if not part_names and not material_names:
                return bad_request("请至少选择一个零件或材料")
            
            csv_content = material_porosity_flow_resistance_service.export_data_to_csv(
                part_names=part_names if part_names else None,
                material_names=material_names if material_names else None
            )
            
            # 创建响应
            output = io.BytesIO()
            output.write(csv_content.encode('utf-8-sig'))  # 使用UTF-8 BOM以支持Excel
            output.seek(0)
            
            # 生成文件名
            parts_str = '_'.join(part_names) if part_names else '全部零件'
            materials_str = '_'.join(material_names) if material_names else '全部材料'
            filename = f"孔隙率流阻数据_{parts_str}_{materials_str}.csv"
            
            return send_file(
                output,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            return error(f"导出数据失败: {str(e)}")
    
    @staticmethod
    @material_porosity_flow_resistance_bp.route('/api/statistics')
    @login_required
    def get_statistics():
        """获取统计信息"""
        try:
            stats = material_porosity_flow_resistance_service.get_statistics()
            return success(stats)
        except Exception as e:
            return error(f"获取统计信息失败: {str(e)}")


# 注册蓝图到应用
def register_material_porosity_flow_resistance_routes(app):
    """注册材料孔隙率流阻路由"""
    app.register_blueprint(material_porosity_flow_resistance_bp)
