/**
 * 多标签页管理模块
 */
class TabManager {
    constructor() {
        this.tabs = new Map();
        this.activeTabId = 'home';
        this.tabCounter = 0;
        this.init();
    }

    /**
     * 初始化标签页管理器
     */
    init() {
        // 确保DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupDOM());
        } else {
            this.setupDOM();
        }
    }

    /**
     * 设置DOM结构
     */
    setupDOM() {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;

        // 创建标签页结构
        const tabsHTML = `
            <!-- 标签页导航 -->
            <ul class="nav nav-tabs mb-3" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="tab-home-btn" data-bs-toggle="tab" 
                            data-bs-target="#tab-home" type="button" role="tab" 
                            aria-controls="tab-home" aria-selected="true">
                        <i class="fas fa-home me-1"></i>首页
                    </button>
                </li>
            </ul>

            <!-- 标签页内容 -->
            <div class="tab-content" id="mainTabContent">
                <div class="tab-pane fade show active" id="tab-home" role="tabpanel" 
                     aria-labelledby="tab-home-btn">
                    <div id="home-content">
                        <!-- 首页内容将被移动到这里 -->
                    </div>
                </div>
            </div>
        `;

        // 保存原有内容
        const originalContent = mainContent.innerHTML;
        
        // 插入标签页结构
        mainContent.innerHTML = tabsHTML;
        
        // 将原有内容移动到首页标签页
        document.getElementById('home-content').innerHTML = originalContent;

        // 注册首页标签页
        this.tabs.set('home', {
            id: 'home',
            title: '首页',
            closable: false,
            loaded: true
        });

        // 绑定事件
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 标签页切换事件
        document.addEventListener('shown.bs.tab', (e) => {
            const tabId = e.target.getAttribute('data-bs-target').replace('#tab-', '');
            this.activeTabId = tabId;
        });

        // 标签页关闭事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-close-btn')) {
                e.preventDefault();
                e.stopPropagation();
                const tabId = e.target.getAttribute('data-tab-id');
                this.closeTab(tabId);
            }
        });
    }

    /**
     * 打开新标签页
     * @param {string} id 标签页ID
     * @param {string} title 标签页标题
     * @param {string} url 内容URL
     * @param {Object} options 选项
     */
    async openTab(id, title, url, options = {}) {
        // 如果标签页已存在，直接切换
        if (this.tabs.has(id)) {
            this.switchTab(id);
            return;
        }

        try {
            // 显示加载状态
            this.showTabLoading(id, title);

            // 加载内容
            const content = await this.loadTabContent(url);

            // 创建标签页
            this.createTab(id, title, content, options);

            // 切换到新标签页
            this.switchTab(id);

        } catch (error) {
            console.error('打开标签页失败:', error);
            showMessage('打开页面失败: ' + error.message, 'error');
            this.removeTabElements(id);
        }
    }

    /**
     * 显示标签页加载状态
     */
    showTabLoading(id, title) {
        const tabsNav = document.getElementById('mainTabs');
        const tabContent = document.getElementById('mainTabContent');

        // 创建标签页按钮
        const tabBtn = document.createElement('li');
        tabBtn.className = 'nav-item';
        tabBtn.innerHTML = `
            <button class="nav-link" id="tab-${id}-btn" data-bs-toggle="tab" 
                    data-bs-target="#tab-${id}" type="button" role="tab" 
                    aria-controls="tab-${id}" aria-selected="false">
                <i class="fas fa-spinner fa-spin me-1"></i>${title}
                <button type="button" class="btn-close btn-close-sm ms-2 tab-close-btn" 
                        data-tab-id="${id}" aria-label="关闭"></button>
            </button>
        `;
        tabsNav.appendChild(tabBtn);

        // 创建内容面板
        const tabPane = document.createElement('div');
        tabPane.className = 'tab-pane fade';
        tabPane.id = `tab-${id}`;
        tabPane.setAttribute('role', 'tabpanel');
        tabPane.setAttribute('aria-labelledby', `tab-${id}-btn`);
        tabPane.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载页面内容...</p>
            </div>
        `;
        tabContent.appendChild(tabPane);
    }

    /**
     * 加载标签页内容
     */
    async loadTabContent(url) {
        const response = await fetch(url + '?ajax=1', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.text();
    }

    /**
     * 创建标签页
     */
    createTab(id, title, content, options = {}) {
        // 根据页面类型选择图标
        const icon = this.getTabIcon(id);

        // 更新标签页按钮
        const tabBtn = document.getElementById(`tab-${id}-btn`);
        if (tabBtn) {
            tabBtn.innerHTML = `
                <i class="${icon} me-1"></i>${title}
                <button type="button" class="btn-close btn-close-sm ms-2 tab-close-btn"
                        data-tab-id="${id}" aria-label="关闭"></button>
            `;
        }

        // 更新内容面板
        const tabPane = document.getElementById(`tab-${id}`);
        if (tabPane) {
            tabPane.innerHTML = content;
        }

        // 注册标签页
        this.tabs.set(id, {
            id: id,
            title: title,
            closable: true,
            loaded: true,
            ...options
        });

        // 执行页面内的脚本
        this.executeScripts(tabPane);
    }

    /**
     * 执行标签页内的脚本
     */
    executeScripts(container) {
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => {
            const newScript = document.createElement('script');
            if (script.src) {
                newScript.src = script.src;
            } else {
                newScript.textContent = script.textContent;
            }
            document.head.appendChild(newScript);
            document.head.removeChild(newScript);
        });
    }

    /**
     * 切换标签页
     */
    switchTab(id) {
        const tabBtn = document.getElementById(`tab-${id}-btn`);
        if (tabBtn) {
            const tab = new bootstrap.Tab(tabBtn);
            tab.show();
        }
    }

    /**
     * 关闭标签页
     */
    closeTab(id) {
        const tab = this.tabs.get(id);
        if (!tab || !tab.closable) {
            return;
        }

        // 如果关闭的是当前活动标签页，切换到首页
        if (this.activeTabId === id) {
            this.switchTab('home');
        }

        // 移除DOM元素
        this.removeTabElements(id);

        // 从管理器中移除
        this.tabs.delete(id);
    }

    /**
     * 移除标签页DOM元素
     */
    removeTabElements(id) {
        const tabBtn = document.getElementById(`tab-${id}-btn`);
        const tabPane = document.getElementById(`tab-${id}`);

        if (tabBtn && tabBtn.parentElement) {
            tabBtn.parentElement.remove();
        }

        if (tabPane) {
            tabPane.remove();
        }
    }

    /**
     * 根据标签页ID获取对应的图标
     */
    getTabIcon(id) {
        const iconMap = {
            'airtightness-comparison': 'fas fa-chart-bar',
            'airtightness-images': 'fas fa-images',
            'area-comparison': 'fas fa-chart-line',
            'home': 'fas fa-home'
        };

        return iconMap[id] || 'fas fa-file-alt';
    }
}

// 创建全局标签页管理器实例
let tabManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    tabManager = new TabManager();
});

// 全局函数：打开标签页
function openTab(id, title, url, options = {}) {
    if (tabManager) {
        tabManager.openTab(id, title, url, options);
    }
}
